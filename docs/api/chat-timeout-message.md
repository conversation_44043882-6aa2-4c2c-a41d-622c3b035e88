# Chat Timeout Message API

## Overview
This API endpoint sends an automated timeout message to a chat room via socket emission. It's designed to be used when a chat room doesn't receive responses within a specified time period.

## Endpoint
```
POST /api/v1/chat/timeout-message
```

## Authentication
Requires a valid JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Request Body
```json
{
  "chat_id": 123
}
```

### Parameters
- `chat_id` (number, required): The ID of the chat room to send the timeout message to

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Timeout message sent successfully",
  "data": {
    "chatId": 123,
    "chatType": "private",
    "botUserId": 999,
    "botUserName": "Bot Assistant",
    "content": "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด"
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "error": "chat_id is required"
}
```
```json
{
  "error": "Invalid chat_id"
}
```

#### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

#### 403 Forbidden
```json
{
  "error": "Cannot send messages to inactive chat"
}
```

#### 404 Not Found
```json
{
  "error": "Chat not found"
}
```
```json
{
  "error": "Bot user not found"
}
```

#### 500 Internal Server Error
```json
{
  "error": "Failed to send timeout message via socket"
}
```
```json
{
  "error": "Internal server error"
}
```

## How It Works

1. **Authentication**: Validates the user's JWT token
2. **Chat Validation**: 
   - Checks if the chat exists
   - Verifies the chat is active
   - Retrieves the chat type (private, task, department, organization)
3. **Bot User Lookup**: Finds the system bot user (where `userRole.isBot = true`)
4. **Socket Emission**: Emits a `send_message` event with the timeout message

## Socket Event Structure
The API emits a `send_message` socket event with the following data:
```javascript
{
  chatId: 123,
  chatType: "private", // lowercase version of the chat type
  userId: 999, // bot user ID
  content: "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด",
  messageType: "text"
}
```

## Usage Example

### Using curl
```bash
curl -X POST http://localhost:3000/api/v1/chat/timeout-message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"chat_id": 123}'
```

### Using JavaScript/fetch
```javascript
const response = await fetch('/api/v1/chat/timeout-message', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    chat_id: 123
  })
});

const result = await response.json();
console.log(result);
```

## Prerequisites

1. **Bot User Setup**: Ensure there's at least one user in the system with `userRole.isBot = true`
2. **Socket Server**: The socket server must be running and accessible
3. **Active Chat**: The target chat must exist and be active

## Integration Notes

- This endpoint follows the same socket emission pattern as regular chat messages
- The timeout message appears as if sent by the bot user
- Real-time updates are handled through the existing socket infrastructure
- The message content is in Thai: "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด" (This chat has no response within the specified time period)

## Error Handling

The API includes comprehensive error handling for:
- Missing or invalid authentication
- Invalid or missing chat_id
- Non-existent chats
- Inactive chats
- Missing bot users
- Socket connection failures

All errors return appropriate HTTP status codes and descriptive error messages.

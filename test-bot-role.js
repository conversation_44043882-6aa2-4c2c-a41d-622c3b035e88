const { PrismaClient } = require('./src/generated/prisma');

async function testBotRole() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🤖 Testing Bot Role Implementation...\n');
    
    // 1. Check if bot role exists in database
    console.log('1. Checking if bot role exists...');
    const botRole = await prisma.userRole.findFirst({
      where: { isBot: true }
    });
    
    if (botRole) {
      console.log('✅ Bot role found:', {
        id: botRole.id,
        name: botRole.name,
        description: botRole.description,
        isBot: botRole.isBot,
        isOwner: botRole.isOwner,
        isAdmin: botRole.isAdmin,
        isMember: botRole.isMember
      });
    } else {
      console.log('❌ Bot role not found in database');
      return;
    }
    
    // 2. Test creating a bot user
    console.log('\n2. Testing bot user creation...');
    const testBotUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'Bot',
        passwordHash: 'dummy-hash',
        userRoleId: botRole.id,
        isBot: true
      },
      include: {
        userRole: true
      }
    });
    
    console.log('✅ Bot user created:', {
      id: testBotUser.id,
      email: testBotUser.email,
      firstName: testBotUser.firstName,
      lastName: testBotUser.lastName,
      isBot: testBotUser.isBot,
      role: {
        name: testBotUser.userRole.name,
        isBot: testBotUser.userRole.isBot
      }
    });
    
    // 3. Test querying bot users
    console.log('\n3. Testing bot user queries...');
    const allBotUsers = await prisma.user.findMany({
      where: { isBot: true },
      include: { userRole: true }
    });
    
    console.log(`✅ Found ${allBotUsers.length} bot user(s):`);
    allBotUsers.forEach(user => {
      console.log(`  - ${user.firstName} ${user.lastName} (${user.email})`);
    });
    
    // 4. Test role-based queries
    console.log('\n4. Testing role-based queries...');
    const usersWithBotRole = await prisma.user.findMany({
      where: {
        userRole: {
          isBot: true
        }
      },
      include: { userRole: true }
    });
    
    console.log(`✅ Found ${usersWithBotRole.length} user(s) with bot role`);
    
    // 5. Clean up test data
    console.log('\n5. Cleaning up test data...');
    await prisma.user.delete({
      where: { id: testBotUser.id }
    });
    console.log('✅ Test bot user deleted');
    
    console.log('\n🎉 All bot role tests passed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testBotRole();

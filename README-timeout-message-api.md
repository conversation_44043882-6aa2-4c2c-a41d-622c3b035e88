# Chat Timeout Message API Setup Guide

## Overview
This guide explains how to set up and use the new Chat Timeout Message API endpoint that sends automated timeout messages to chat rooms via socket emission.

## Files Created

### 1. API Route Handler
- **File**: `src/app/api/v1/chat/timeout-message/route.ts`
- **Purpose**: Main API endpoint that handles timeout message requests

### 2. Test Suite
- **File**: `src/app/api/v1/chat/timeout-message/route.test.ts`
- **Purpose**: Comprehensive unit tests for the API endpoint

### 3. Documentation
- **File**: `docs/api/chat-timeout-message.md`
- **Purpose**: Detailed API documentation with examples

### 4. Testing Scripts
- **File**: `scripts/test-timeout-message.js`
- **Purpose**: Manual testing script for the API endpoint

### 5. Bot User Setup
- **File**: `scripts/create-bot-user.ts`
- **Purpose**: Script to create the required bot user in the database

## Prerequisites

### 1. Database Setup
Ensure your database has been migrated and seeded:

```bash
# Run migrations
npm run migrate:dev

# Run seed script to create roles and basic data
npm run db:seed:dev
```

### 2. Create Bot User
The API requires a bot user to exist in the database. Run the bot user creation script:

```bash
# Create the bot user
npx ts-node scripts/create-bot-user.ts
```

This will create a system bot user with:
- Email: `<EMAIL>`
- Name: `System Bot`
- Role: Bot (with `isBot: true`)

### 3. Socket Server
Ensure your socket server is running and the `NEXT_PUBLIC_SOCKET_URL` environment variable is configured.

## API Usage

### Endpoint
```
POST /api/v1/chat/timeout-message
```

### Request
```bash
curl -X POST http://localhost:3000/api/v1/chat/timeout-message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"chat_id": 123}'
```

### Response
```json
{
  "success": true,
  "message": "Timeout message sent successfully",
  "data": {
    "chatId": 123,
    "chatType": "private",
    "botUserId": 999,
    "botUserName": "System Bot",
    "content": "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด"
  }
}
```

## Testing

### Manual Testing
Use the provided test script:

```bash
# Test with a specific chat ID and JWT token
node scripts/test-timeout-message.js 123 "your-jwt-token" "http://localhost:3000"
```

### Unit Tests
If you have a testing framework set up, run:

```bash
# Example with Jest (if configured)
npm test src/app/api/v1/chat/timeout-message/route.test.ts
```

## How It Works

1. **Authentication**: Validates the user's JWT token
2. **Chat Validation**: 
   - Verifies the chat exists and is active
   - Retrieves the chat type (private, task, department, organization)
3. **Bot User Lookup**: Finds the system bot user (where `userRole.isBot = true`)
4. **Socket Emission**: Emits a `send_message` event with the timeout message

## Socket Event Structure

The API emits a `send_message` socket event with:

```javascript
{
  chatId: 123,
  chatType: "private", // lowercase version of chat type
  userId: 999, // bot user ID
  content: "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด",
  messageType: "text"
}
```

## Error Handling

The API handles various error scenarios:

- **400**: Missing or invalid `chat_id`
- **401**: Authentication required or invalid token
- **403**: Chat is inactive
- **404**: Chat not found or bot user not found
- **500**: Socket emission failure or internal server error

## Integration Example

Here's how you might integrate this into a timeout monitoring system:

```javascript
// Example timeout monitoring function
async function handleChatTimeout(chatId, token) {
  try {
    const response = await fetch('/api/v1/chat/timeout-message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ chat_id: chatId })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Timeout message sent:', result.data.content);
    } else {
      const error = await response.json();
      console.error('Failed to send timeout message:', error.error);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}

// Use in a timeout monitoring system
setTimeout(() => {
  handleChatTimeout(123, userToken);
}, 30000); // 30 seconds timeout
```

## Troubleshooting

### Common Issues

1. **"Bot user not found" error**
   - Solution: Run `npx ts-node scripts/create-bot-user.ts`

2. **"Failed to send timeout message via socket" error**
   - Check if socket server is running
   - Verify `NEXT_PUBLIC_SOCKET_URL` environment variable

3. **"Chat not found" error**
   - Verify the chat ID exists in the database
   - Check if the chat is active

4. **Authentication errors**
   - Ensure JWT token is valid and not expired
   - Check if the user exists in the database

### Debugging

Enable debug logging by checking the server console for:
- Socket connection status
- Database query results
- Error messages with stack traces

## Security Considerations

- The API requires authentication for all requests
- Only active chats can receive timeout messages
- The bot user has minimal privileges (no admin/owner rights)
- Socket emissions are logged for debugging purposes

## Future Enhancements

Potential improvements for the API:
- Configurable timeout message content
- Support for different message types (not just text)
- Rate limiting to prevent spam
- Audit logging for timeout message events
- Integration with notification systems

import { PrismaClient } from '../src/generated/prisma';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

async function setupTimeoutTest() {
  try {
    console.log('🚀 Setting up timeout message test environment...');

    // 1. Ensure bot user exists
    console.log('1️⃣ Checking for bot user...');
    let botUser = await prisma.user.findFirst({
      where: {
        userRole: {
          isBot: true
        }
      },
      include: {
        userRole: true
      }
    });

    if (!botUser) {
      console.log('Creating bot user...');
      
      // First ensure bot role exists
      let botRole = await prisma.userRole.findFirst({
        where: { isBot: true }
      });

      if (!botRole) {
        console.log('Creating bot role...');
        botRole = await prisma.userRole.create({
          data: {
            name: '<PERSON><PERSON>',
            isOwner: false,
            isAdmin: false,
            isMember: false,
            isBot: true,
          }
        });
      }

      const passwordHash = await bcrypt.hash('bot-user-no-login', 10);
      botUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: passwordHash,
          firstName: 'System',
          lastName: 'Bot',
          phone: null,
          imageUrl: null,
          userRoleId: botRole.id,
          isAdmin: false,
          isMember: false,
          isOwner: false,
          isBot: true,
        },
        include: {
          userRole: true
        }
      });
    }

    console.log('✅ Bot user ready:', {
      id: botUser.id,
      email: botUser.email,
      name: `${botUser.firstName} ${botUser.lastName}`,
      isBot: botUser.isBot
    });

    // 2. Create or find test user
    console.log('2️⃣ Setting up test user...');
    let testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    });

    if (!testUser) {
      console.log('Creating test user...');
      
      // Ensure member role exists
      let memberRole = await prisma.userRole.findFirst({
        where: { isMember: true }
      });

      if (!memberRole) {
        console.log('Creating member role...');
        memberRole = await prisma.userRole.create({
          data: {
            name: 'Member',
            isOwner: false,
            isAdmin: false,
            isMember: true,
            isBot: false,
          }
        });
      }

      const passwordHash = await bcrypt.hash('testpassword', 10);
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: passwordHash,
          firstName: 'Test',
          lastName: 'User',
          phone: '1234567890',
          imageUrl: null,
          userRoleId: memberRole.id,
          isAdmin: false,
          isMember: true,
          isOwner: false,
          isBot: false,
        }
      });
    }

    console.log('✅ Test user ready:', {
      id: testUser.id,
      email: testUser.email,
      name: `${testUser.firstName} ${testUser.lastName}`
    });

    // 3. Create test chat
    console.log('3️⃣ Creating test chat...');
    const testChat = await prisma.chat.create({
      data: {
        name: 'Timeout Test Chat',
        chatType: 'PRIVATE',
        isActive: true,
        createdBy: testUser.id,
        lastMessageAt: new Date(),
      }
    });

    // 4. Add users to chat
    console.log('4️⃣ Adding users to chat...');
    await prisma.chatUser.createMany({
      data: [
        {
          chatId: testChat.id,
          userId: testUser.id,
          isAdmin: true,
          joinedAt: new Date(),
        },
        {
          chatId: testChat.id,
          userId: botUser.id,
          isAdmin: false,
          joinedAt: new Date(),
        }
      ],
      skipDuplicates: true
    });

    // 5. Generate JWT token
    console.log('5️⃣ Generating JWT token...');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    
    const testToken = jwt.sign(
      {
        userId: testUser.id,
        email: testUser.email,
        isOwner: testUser.isOwner,
        isAdmin: testUser.isAdmin,
        isMember: testUser.isMember,
        isBot: testUser.isBot,
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('✅ Test environment setup completed!');
    console.log('');
    console.log('📋 Test Information:');
    console.log('🤖 Bot User ID:', botUser.id);
    console.log('👤 Test User ID:', testUser.id);
    console.log('💬 Test Chat ID:', testChat.id);
    console.log('🔑 JWT Token:', testToken.substring(0, 50) + '...');
    console.log('');
    console.log('🧪 To test the timeout message API:');
    console.log(`node scripts/test-timeout-message.js ${testChat.id} "${testToken}"`);
    console.log('');
    console.log('🔍 Expected behavior:');
    console.log('- Message should be saved to database');
    console.log('- Socket event should be emitted (if socket server is running)');
    console.log('- Thai timeout message should appear in chat interface');

    return {
      botUser,
      testUser,
      testChat,
      testToken
    };

  } catch (error) {
    console.error('❌ Error setting up test environment:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  setupTimeoutTest()
    .then(() => {
      console.log('✅ Test environment setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test environment setup failed:', error);
      process.exit(1);
    });
}

export { setupTimeoutTest };

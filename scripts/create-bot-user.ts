import { PrismaClient } from '../src/generated/prisma';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function createBotUser() {
  try {
    console.log('🤖 Creating bot user...');

    // Check if bot role exists
    const botRole = await prisma.userRole.findFirst({
      where: { isBot: true }
    });

    if (!botRole) {
      console.error('❌ Bot role not found. Please run the seed script first.');
      process.exit(1);
    }

    console.log('✅ Found bot role:', botRole.name);

    // Check if bot user already exists
    const existingBotUser = await prisma.user.findFirst({
      where: {
        userRole: {
          isBot: true
        }
      }
    });

    if (existingBotUser) {
      console.log('✅ Bot user already exists:', {
        id: existingBotUser.id,
        email: existingBotUser.email,
        name: `${existingBotUser.firstName} ${existingBotUser.lastName}`
      });
      return existingBotUser;
    }

    // Create a dummy password hash for the bot user
    const dummyPassword = 'bot-user-no-login';
    const passwordHash = await bcrypt.hash(dummyPassword, 10);

    // Create the bot user
    const botUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: passwordHash,
        firstName: 'System',
        lastName: 'Bot',
        phone: null,
        imageUrl: null,
        userRoleId: botRole.id,
        isAdmin: false,
        isMember: false,
        isOwner: false,
        isBot: true,
      },
      include: {
        userRole: true
      }
    });

    console.log('🎉 Successfully created bot user:', {
      id: botUser.id,
      email: botUser.email,
      name: `${botUser.firstName} ${botUser.lastName}`,
      role: botUser.userRole.name,
      isBot: botUser.isBot
    });

    return botUser;

  } catch (error) {
    console.error('❌ Error creating bot user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  createBotUser()
    .then(() => {
      console.log('✅ Bot user creation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Bot user creation failed:', error);
      process.exit(1);
    });
}

export { createBotUser };

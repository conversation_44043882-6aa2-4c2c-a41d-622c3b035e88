#!/usr/bin/env node

/**
 * Manual test script for the chat timeout message API endpoint
 * 
 * Usage:
 * node scripts/test-timeout-message.js <chat_id> <jwt_token> [base_url]
 * 
 * Example:
 * node scripts/test-timeout-message.js 123 "your-jwt-token" "http://localhost:3000"
 */

const https = require('https');
const http = require('http');

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  console.error('Usage: node scripts/test-timeout-message.js <chat_id> <jwt_token> [base_url]');
  console.error('Example: node scripts/test-timeout-message.js 123 "your-jwt-token" "http://localhost:3000"');
  process.exit(1);
}

const chatId = parseInt(args[0]);
const jwtToken = args[1];
const baseUrl = args[2] || 'http://localhost:3000';

if (isNaN(chatId)) {
  console.error('Error: chat_id must be a valid number');
  process.exit(1);
}

// Parse the base URL
const url = new URL(`${baseUrl}/api/v1/chat/timeout-message`);
const isHttps = url.protocol === 'https:';
const httpModule = isHttps ? https : http;

// Prepare the request data
const postData = JSON.stringify({
  chat_id: chatId
});

// Prepare the request options
const options = {
  hostname: url.hostname,
  port: url.port || (isHttps ? 443 : 80),
  path: url.pathname,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${jwtToken}`,
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('🚀 Testing Chat Timeout Message API');
console.log('📍 URL:', `${baseUrl}/api/v1/chat/timeout-message`);
console.log('💬 Chat ID:', chatId);
console.log('🔑 Token:', jwtToken.substring(0, 20) + '...');
console.log('');

// Make the request
const req = httpModule.request(options, (res) => {
  console.log('📊 Response Status:', res.statusCode);
  console.log('📋 Response Headers:', JSON.stringify(res.headers, null, 2));
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('✅ Response Body:');
      console.log(JSON.stringify(response, null, 2));
      
      if (res.statusCode === 200) {
        console.log('');
        console.log('🎉 SUCCESS: Timeout message sent successfully!');
        console.log('📨 Message Content:', response.data?.content);
        console.log('🤖 Bot User ID:', response.data?.botUserId);
        console.log('💬 Chat Type:', response.data?.chatType);
      } else {
        console.log('');
        console.log('❌ ERROR: Request failed');
        console.log('🔍 Error Details:', response.error || 'Unknown error');
      }
    } catch (error) {
      console.log('❌ Failed to parse JSON response:');
      console.log(data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
  process.exit(1);
});

// Send the request
req.write(postData);
req.end();

// Add timeout handling
setTimeout(() => {
  console.error('⏰ Request timeout after 30 seconds');
  process.exit(1);
}, 30000);

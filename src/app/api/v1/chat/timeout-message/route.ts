import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

/**
 * POST API to send an automated timeout message to a chat room
 *
 * Request body:
 * {
 *   "chat_id": 123 (required) - The ID of the chat room to send the timeout message to
 * }
 *
 * This endpoint:
 * 1. Validates the chat exists and gets its type
 * 2. Finds the bot user (where userRole.isBot = true)
 * 3. Emits a socket event to send the timeout message
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { chat_id } = body;

    // Validate required fields
    if (!chat_id) {
      return NextResponse.json({ error: 'chat_id is required' }, { status: 400 });
    }

    const chatIdNum = Number(chat_id);
    if (isNaN(chatIdNum)) {
      return NextResponse.json({ error: 'Invalid chat_id' }, { status: 400 });
    }

    // Fetch chat details to get chat_type
    const chat = await prisma.chat.findUnique({
      where: { id: chatIdNum },
      select: {
        id: true,
        chatType: true,
        isActive: true,
      },
    });

    if (!chat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    if (!chat.isActive) {
      return NextResponse.json({ error: 'Cannot send messages to inactive chat' }, { status: 403 });
    }

    // Find the bot user (where userRole.isBot = true)
    const botUser = await prisma.user.findFirst({
      where: {
        userRole: {
          isBot: true,
        },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
      },
    });

    if (!botUser) {
      return NextResponse.json({ error: 'Bot user not found' }, { status: 404 });
    }

    // First, save the message to the database
    const timeoutMessage = await prisma.chatMessage.create({
      data: {
        chatId: chatIdNum,
        userId: botUser.id,
        content: 'chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด',
        messageType: 'TEXT',
        messageStatus: 'DELIVERED',
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
                isBot: true,
              },
            },
          },
        },
      },
    });

    // Update chat's last message timestamp
    await prisma.chat.update({
      where: { id: chatIdNum },
      data: { lastMessageAt: new Date() },
    });

    // Prepare the socket message data in the format expected by the client
    const newMessageData = {
      messageId: timeoutMessage.id,
      chatId: chatIdNum,
      chatType: chat.chatType.toLowerCase(),
      sender: {
        id: timeoutMessage.user.id,
        firstName: timeoutMessage.user.firstName,
        lastName: timeoutMessage.user.lastName,
        imageUrl: timeoutMessage.user.imageUrl,
        userRole: timeoutMessage.user.userRole,
      },
      content: timeoutMessage.content,
      messageType: timeoutMessage.messageType.toLowerCase(),
      createdAt: timeoutMessage.createdAt,
      status: timeoutMessage.messageStatus.toLowerCase(),
    };

    // Emit socket event for real-time updates
    let socketEmitted = false;
    try {
      console.log('Attempting to emit socket message:', newMessageData);
      socketEmitted = await socketServerService.emit('new_message', newMessageData);

      if (socketEmitted) {
        console.log('Timeout message emitted successfully via socket');
      } else {
        console.warn('Socket emission failed, but message was saved to database');
      }
    } catch (socketError) {
      console.error('Failed to emit timeout message via socket:', socketError);
      // Don't return error here - socket failure shouldn't break the API
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Timeout message sent successfully',
        data: {
          messageId: timeoutMessage.id,
          chatId: chat_id,
          chatType: chat.chatType.toLowerCase(),
          botUserId: botUser.id,
          botUserName: `${botUser.firstName} ${botUser.lastName}`,
          content: 'chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด',
          createdAt: timeoutMessage.createdAt,
          socketEmitted: socketEmitted,
          socketConnected: socketServerService.connected,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error sending timeout message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

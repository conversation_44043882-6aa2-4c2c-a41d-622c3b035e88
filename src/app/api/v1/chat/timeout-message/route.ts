import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

/**
 * POST API to send an automated timeout message to a chat room
 *
 * Request body:
 * {
 *   "chat_id": 123 (required) - The ID of the chat room to send the timeout message to
 * }
 *
 * This endpoint:
 * 1. Validates the chat exists and gets its type
 * 2. Finds the bot user (where userRole.isBot = true)
 * 3. Emits a socket event to send the timeout message
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { chat_id } = body;

    // Validate required fields
    if (!chat_id) {
      return NextResponse.json({ error: 'chat_id is required' }, { status: 400 });
    }

    const chatIdNum = Number(chat_id);
    if (isNaN(chatIdNum)) {
      return NextResponse.json({ error: 'Invalid chat_id' }, { status: 400 });
    }

    // Fetch chat details to get chat_type
    const chat = await prisma.chat.findUnique({
      where: { id: chatIdNum },
      select: {
        id: true,
        chatType: true,
        isActive: true,
      },
    });

    if (!chat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    if (!chat.isActive) {
      return NextResponse.json({ error: 'Cannot send messages to inactive chat' }, { status: 403 });
    }

    // Find the bot user (where userRole.isBot = true)
    const botUser = await prisma.user.findFirst({
      where: {
        userRole: {
          isBot: true,
        },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
      },
    });

    if (!botUser) {
      return NextResponse.json({ error: 'Bot user not found' }, { status: 404 });
    }

    // Emit socket event to send the timeout message
    const messageData = {
      chatId: chat_id,
      chatType: chat.chatType.toLowerCase(), // Convert to lowercase to match frontend expectations
      userId: botUser.id,
      content: "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด",
      messageType: "text",
    };

    try {
      socketServerService.emit('send_message', messageData);
      console.log('Timeout message emitted successfully:', messageData);
    } catch (socketError) {
      console.error('Failed to emit timeout message:', socketError);
      return NextResponse.json(
        { error: 'Failed to send timeout message via socket' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Timeout message sent successfully',
      data: {
        chatId: chat_id,
        chatType: chat.chatType.toLowerCase(),
        botUserId: botUser.id,
        botUserName: `${botUser.firstName} ${botUser.lastName}`,
        content: "chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด",
      },
    }, { status: 200 });

  } catch (error) {
    console.error('Error sending timeout message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

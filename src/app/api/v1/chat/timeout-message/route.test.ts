import { NextRequest } from 'next/server';
import { POST } from './route';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    chat: {
      findUnique: jest.fn(),
    },
    user: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('@/lib/auth-utils', () => ({
  authenticateUser: jest.fn(),
}));

jest.mock('@/lib/socket-server', () => ({
  emit: jest.fn(),
}));

// Helper function to create mock request
function createMockRequest(body: any, headers: Record<string, string> = {}) {
  return {
    json: async () => body,
    headers: {
      get: (name: string) => headers[name] || null,
    },
  } as NextRequest;
}

describe('/api/v1/chat/timeout-message', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST', () => {
    const mockAuth = {
      userId: 1,
      isOwner: false,
      isAdmin: false,
      isMember: true,
      isBot: false,
    };

    const mockChat = {
      id: 123,
      chatType: 'PRIVATE',
      isActive: true,
    };

    const mockBotUser = {
      id: 999,
      firstName: 'Bot',
      lastName: 'Assistant',
    };

    it('should successfully send timeout message', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue(mockChat);
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(mockBotUser);
      (socketServerService.emit as jest.Mock).mockImplementation(() => {});

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Timeout message sent successfully');
      expect(data.data.chatId).toBe(123);
      expect(data.data.chatType).toBe('private');
      expect(data.data.botUserId).toBe(999);
      expect(data.data.content).toBe('chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด');

      expect(socketServerService.emit).toHaveBeenCalledWith('send_message', {
        chatId: 123,
        chatType: 'private',
        userId: 999,
        content: 'chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด',
        messageType: 'text',
      });
    });

    it('should return 401 when authentication fails', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue({
        error: 'Authentication required',
        status: 401,
      });

      const request = createMockRequest({ chat_id: 123 });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });

    it('should return 400 when chat_id is missing', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);

      const request = createMockRequest(
        {},
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('chat_id is required');
    });

    it('should return 400 when chat_id is invalid', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);

      const request = createMockRequest(
        { chat_id: 'invalid' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid chat_id');
    });

    it('should return 404 when chat is not found', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue(null);

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Chat not found');
    });

    it('should return 403 when chat is inactive', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue({
        ...mockChat,
        isActive: false,
      });

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Cannot send messages to inactive chat');
    });

    it('should return 404 when bot user is not found', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue(mockChat);
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(null);

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Bot user not found');
    });

    it('should return 500 when socket emission fails', async () => {
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue(mockChat);
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(mockBotUser);
      (socketServerService.emit as jest.Mock).mockImplementation(() => {
        throw new Error('Socket connection failed');
      });

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to send timeout message via socket');
    });

    it('should handle different chat types correctly', async () => {
      const taskChat = { ...mockChat, chatType: 'TASK' };
      
      (authenticateUser as jest.Mock).mockResolvedValue(mockAuth);
      (prisma.chat.findUnique as jest.Mock).mockResolvedValue(taskChat);
      (prisma.user.findFirst as jest.Mock).mockResolvedValue(mockBotUser);
      (socketServerService.emit as jest.Mock).mockImplementation(() => {});

      const request = createMockRequest(
        { chat_id: 123 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.chatType).toBe('task');
      expect(socketServerService.emit).toHaveBeenCalledWith('send_message', {
        chatId: 123,
        chatType: 'task',
        userId: 999,
        content: 'chat นี้ไม่มีการตอบสนองในช่วงเวลาที่กำหนด',
        messageType: 'text',
      });
    });
  });
});

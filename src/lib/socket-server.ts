import io from 'socket.io-client';

type SocketType = ReturnType<typeof io>;

/**
 * Server-side socket utility for emitting events from API endpoints
 * This connects to the socket server to emit notifications for real-time updates
 */

let socketInstance: SocketType | null = null;
let isConnected = false;
let connectionPromise: Promise<SocketType> | null = null;

/**
 * Initialize socket connection
 */
function initializeConnection(): Promise<SocketType> {
  if (connectionPromise) {
    return connectionPromise;
  }

  connectionPromise = new Promise((resolve, reject) => {
    try {
      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL;
      if (!socketUrl) {
        console.warn('NEXT_PUBLIC_SOCKET_URL not configured, socket emissions will be skipped');
        reject(new Error('Socket URL not configured'));
        return;
      }

      // Use a system bot user ID - this should match the actual bot user in the database
      const botUserId = 3;
      const fullSocketUrl = `${socketUrl}?userId=${botUserId}`;

      console.log('Initializing server-side socket connection to:', fullSocketUrl);

      const socket = io(fullSocketUrl, {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000,
      });

      socket.on('connect', () => {
        console.log('Server-side socket connected successfully');
        isConnected = true;
        socketInstance = socket;
        resolve(socket);
      });

      socket.on('disconnect', (reason: any) => {
        console.log('Server-side socket disconnected:', reason);
        isConnected = false;
      });

      socket.on('connect_error', (error: Error) => {
        console.error('Server-side socket connection error:', error);
        isConnected = false;
        reject(error);
      });

      socket.on('reconnect', (attemptNumber: any) => {
        console.log('Server-side socket reconnected after', attemptNumber, 'attempts');
        isConnected = true;
      });

      socket.on('reconnect_error', (error: Error) => {
        console.error('Server-side socket reconnection error:', error);
      });

      socket.on('reconnect_failed', () => {
        console.error('Server-side socket failed to reconnect after all attempts');
        isConnected = false;
      });

      // Set a timeout for initial connection
      setTimeout(() => {
        if (!isConnected) {
          reject(new Error('Socket connection timeout'));
        }
      }, 10000);

    } catch (error) {
      console.error('Failed to initialize server-side socket connection:', error);
      reject(error);
    }
  });

  return connectionPromise;
}

/**
 * Get or create socket connection
 */
async function getSocket(): Promise<SocketType | null> {
  try {
    if (socketInstance && isConnected) {
      return socketInstance;
    }

    return await initializeConnection();
  } catch (error) {
    console.error('Failed to get socket connection:', error);
    return null;
  }
}

/**
 * Emit a notification event for real-time updates
 * @param service - The service name (e.g., 'task', 'assistant')
 * @param id - The record ID
 * @param action - The action performed (CREATE, UPDATE, DELETE)
 */
export async function emitNotification(
  service: string,
  id: number | string,
  action: 'CREATE' | 'UPDATE' | 'DELETE' = 'UPDATE'
): Promise<boolean> {
  try {
    const socket = await getSocket();
    if (!socket) {
      console.warn('Socket not available, skipping notification emission');
      return false;
    }

    socket.emit('send_notification', {
      service,
      id,
      action,
      timestamp: new Date().toISOString(),
    });

    console.log(`Emitted notification: ${service}:${id}:${action}`);
    return true;
  } catch (error) {
    console.error('Failed to emit socket notification:', error);
    return false;
  }
}

/**
 * Emit a specific event with custom data
 * @param eventName - The event name to emit
 * @param data - The data to send with the event
 */
export async function emit(eventName: string, data: any): Promise<boolean> {
  try {
    const socket = await getSocket();
    if (!socket) {
      console.warn('Socket not available, skipping event emission');
      return false;
    }

    socket.emit(eventName, data);
    console.log(`Emitted event: ${eventName}`, data);
    return true;
  } catch (error) {
    console.error(`Failed to emit socket event ${eventName}:`, error);
    return false;
  }
}

/**
 * Check if socket is connected
 */
export function isSocketConnected(): boolean {
  return isConnected && socketInstance !== null;
}

/**
 * Disconnect the socket
 */
export function disconnect(): void {
  if (socketInstance) {
    socketInstance.disconnect();
    socketInstance = null;
    isConnected = false;
    connectionPromise = null;
  }
}

// Legacy compatibility - create an object with the same interface
const socketServerService = {
  emit,
  emitNotification,
  get connected() {
    return isSocketConnected();
  },
  disconnect,
};

export default socketServerService;
